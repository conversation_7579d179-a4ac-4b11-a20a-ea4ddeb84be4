<template>
  <q-page class="q-pa-md">
    <div class="test-grid-container">
      <div
        v-for="group in data.groups"
        :key="group.title"
        class="group-subgrid"
      >
        <!-- Header row with avatar and title -->
        <div class="avatar-cell">
          <q-avatar size="40px">
            <img src="/img/default_event.webp" alt="Event Avatar" />
          </q-avatar>
        </div>
        <div class="title-cell">
          {{ group.title }}
        </div>

        <!-- Data rows -->
        <div
          v-for="(item, index) in group.subItems"
          :key="index"
          class="data-row"
        >
          <div class="empty-cell"></div>
          <div class="data-cell">{{ item.cellA }}</div>
          <div class="data-cell">{{ item.cellB }}</div>
          <div class="data-cell">{{ item.cellC }}</div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from "vue";

interface SubItem {
  cellA: string;
  cellB: string;
  cellC: string;
}

interface Group {
  title: string;
  subItems: SubItem[];
}

interface TestData {
  groups: Group[];
}

//Generate random string helper
const generateRandomString = (min: number, max: number): string => {
  const length = Math.floor(Math.random() * (max - min + 1)) + min;
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

//Generate fake test data
const data = ref<TestData>({
  groups: [
    {
      title: "Presidential Election 2024 Markets",
      subItems: [
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        }
      ]
    },
    {
      title: "Cryptocurrency Price Predictions",
      subItems: [
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        }
      ]
    },
    {
      title: "Sports Betting Markets",
      subItems: [
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        }
      ]
    }
  ]
});
</script>

<style scoped>
.test-grid-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: 800px;
  margin: 0 auto;
}

.group-subgrid {
  display: grid;
  grid-template-columns: 60px 1fr 1fr 1fr;
  gap: 8px;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
}

.avatar-cell {
  grid-row: 1 / -1;
  grid-column: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 4px;
  border: 2px solid red;
}

.title-cell {
  grid-column: 2 / -1;
  grid-row: 1;
  font-weight: bold;
  font-size: 16px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 8px;
  border: 2px solid blue;
}

.data-row {
  display: contents;
}

.empty-cell {
  grid-column: 1;
  border: 2px solid orange;
}

.data-cell {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  min-height: 32px;
  display: flex;
  align-items: center;
  border: 2px solid green;
}
</style>
